services:
  ftp:
    image: docker.rsinfo.ac.cn/ftp_server
    ports:
      - "21:21"
      - "990:990"
      - "50000-50020:50000-50020"
    volumes:
      - ./proftpd.conf:/etc/proftpd/proftpd.conf
      - ./ldap.conf:/etc/ldap/ldap.conf
      # - ./nsswitch.conf:/etc/nsswitch.conf
      - ./ssl:/etc/proftpd/ssl:Z
      - ./modules.conf:/etc/proftpd/modules.conf
      # - ./common-auth:/etc/pam.d/common-auth
      # - ./common-account:/etc/pam.d/common-account
      # - ./common-password:/etc/pam.d/common-password
      # - ./auth.proftpd:/etc/pam.d/proftpd
      - /mnt/data10:/mnt/data10:Z
      - ./logs:/ftplog:Z
    # environment:
      # - DEBIAN_FRONTEND=noninteractive
    # restart: always
