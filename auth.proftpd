#%PAM-1.0
auth       required     pam_listfile.so item=user sense=deny file=/etc/ftpusers onerr=succeed
@include common-auth

# This is disabled because anonymous logins will fail otherwise,
# unless you give the 'ftp' user a valid shell, or /bin/false and add
# /bin/false to /etc/shells.
#auth       required    pam_shells.so

@include common-account
@include common-session-noninteractive

auth    required    pam_ldap.so
account required    pam_ldap.so
password required   pam_ldap.so
