# 基于 Ubuntu 的基础镜像
FROM ubuntu:22.04

# 环境变量，防止交互式安装
ENV DEBIAN_FRONTEND=noninteractive

# 更新并安装必要的软件包
RUN apt-get update && apt-get install -y \
    proftpd \
    proftpd-mod-ldap \
    libnss-ldap \
    libpam-ldap \
    ldap-utils \
    openssl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*


# 暴露 FTP 和 FTPS 端口
EXPOSE 21 990 50000-50010

# 启动脚本，动态加载配置
COPY start.sh /start.sh
RUN chmod +x /start.sh

# 容器启动时运行 FTP 服务
CMD ["/start.sh"]
