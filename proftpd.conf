# Proftpd sample configuration for LDAP authentication.
#
# (This is not to be used if you prefer a PAM-based LDAP authentication)
#

# Load LDAP module
LoadModule mod_ldap.c

ServerName                      "ProFTPD FTP Server for VAP"
ServerType                      standalone
DefaultServer                   on
DefaultRoot /mnt
PassivePorts                  50000 50020
# MasqueradeAddress	**************
MasqueradeAddress	**********
TransferLog /ftplog/xferlog
LogFormat transfer "%t %h %u %f %s %b"
AllowOverwrite on
# ExtendedLog /mnt/logs/xferlog STATS

# LDAP Authentication
AuthOrder mod_ldap.c

<IfModule mod_ldap.c>
    LDAPLog /ftplog/ldap.log
    LDAPServer bigrs-info.com
    LDAPAuthBinds on
    LDAPBindDN "cn=test,cn=Users,dc=bigrs-info,dc=com" Radi8188
    LDAPSearchScope subtree
    # LDAPSearchScope base
    LDAPAttr uid sAMAccountName
    LDAPAttr uidNumber uidNumber
    LDAPAttr gidNumber gidNumber
    RequireValidShell off
    LDAPGenerateHomedir on
    LDAPUsers cn=Users,dc=bigrs-info,dc=com (sAMAccountName=%u)
    # LDAPUsers ou=vap,dc=bigrs-info,dc=com (sAMAccountName=%u)
    # LDAPUsers dc=bigrs-info,dc=com (&(objectClass=user)(sAMAccountName=%u)(|(distinguishedName=CN=Users,dc=bigrs-info,dc=com)(distinguishedName=OU=SS,dc=bigrs-info,dc=com)))
    # LDAPUsers ou=Students,dc=bigrs-info,dc=com (sAMAccountName=%u)
    # LDAPUsers dc=bigrs-info,dc=com (sAMAccountName=%u)
    LDAPGenerateHomedirPrefix /mnt
    LDAPGenerateHomedirPrefixNoUsername on
    LDAPDefaultGID 14000
</IfModule>

# <IfModule mod_ldap.c>
#     LDAPLog /var/log/proftpd/ldap.log
#     LDAPServer bigrs-info.com
#     LDAPAuthBinds on
#     LDAPBindDN "cn=test,cn=Users,dc=bigrs-info,dc=com" Radi8188
#     LDAPSearchScope subtree
#     # LDAPSearchScope base
#     LDAPAttr uid sAMAccountName
#     LDAPAttr gidNumber gidNumber
#     RequireValidShell off
#     LDAPGenerateHomedir on
#     # LDAPUsers cn=Users,dc=bigrs-info,dc=com (sAMAccountName=%u)
#     # LDAPUsers dc=bigrs-info,dc=com (&(objectClass=user)(sAMAccountName=%u)(|(distinguishedName=CN=Users,dc=bigrs-info,dc=com)(distinguishedName=OU=SS,dc=bigrs-info,dc=com)))
#     LDAPUsers ou=Students,dc=bigrs-info,dc=com (sAMAccountName=%u)
#     # LDAPUsers dc=bigrs-info,dc=com (sAMAccountName=%u)
#     LDAPGenerateHomedirPrefix /mnt
#     LDAPGenerateHomedirPrefixNoUsername on
#     LDAPDefaultGID 14000
# </IfModule>


