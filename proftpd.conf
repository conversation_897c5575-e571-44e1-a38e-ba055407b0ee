# ProFTPD configuration for LDAP authentication
#
# This configuration enables LDAP authentication against Active Directory
# LDAP Server: bigrs-info.com
# User search base: cn=Users,dc=bigrs-info,dc=com
#
# Key features:
# - LDAP authentication with bind authentication
# - Automatic home directory generation under /mnt
# - User mapping: sAMAccountName -> uid, uidNumber -> uidNumber, gidNumber -> gidNumber
# - Default GID: 14000 for users without gidNumber
#

# Load LDAP module
LoadModule mod_ldap.c

ServerName                      "ProFTPD FTP Server for VAP"
ServerType                      standalone
DefaultServer                   on
DefaultRoot /mnt
PassivePorts                  50000 50020
# MasqueradeAddress	**************
MasqueradeAddress	**********
TransferLog /ftplog/xferlog
LogFormat transfer "%t %h %u %f %s %b"
AllowOverwrite on
# ExtendedLog /mnt/logs/xferlog STATS

# LDAP Authentication
AuthOrder mod_ldap.c

<IfModule mod_ldap.c>
    # LDAPLog /ftplog/ldap.log
    LDAPServer bigrs-info.com
    LDAPAuthBinds on
    LDAPBindDN "cn=test,cn=Users,dc=bigrs-info,dc=com" Radi8188
    LDAPSearchScope subtree
    # LDAPSearchScope base
    LDAPAttr uid sAMAccountName
    LDAPAttr uidNumber uidNumber
    LDAPAttr gidNumber gidNumber
    RequireValidShell off
    LDAPGenerateHomedir on
    LDAPUsers cn=Users,dc=bigrs-info,dc=com (sAMAccountName=%u)
    # LDAPUsers ou=vap,dc=bigrs-info,dc=com (sAMAccountName=%u)
    # LDAPUsers dc=bigrs-info,dc=com (&(objectClass=user)(sAMAccountName=%u)(|(distinguishedName=CN=Users,dc=bigrs-info,dc=com)(distinguishedName=OU=SS,dc=bigrs-info,dc=com)))
    # LDAPUsers ou=Students,dc=bigrs-info,dc=com (sAMAccountName=%u)
    # LDAPUsers dc=bigrs-info,dc=com (sAMAccountName=%u)
    LDAPGenerateHomedirPrefix /mnt
    LDAPGenerateHomedirPrefixNoUsername on
    LDAPDefaultGID 14000
</IfModule>

# Alternative LDAP configurations (commented out)
# For different user search paths, uncomment and modify as needed:
# LDAPUsers ou=vap,dc=bigrs-info,dc=com (sAMAccountName=%u)
# LDAPUsers ou=Students,dc=bigrs-info,dc=com (sAMAccountName=%u)
# LDAPUsers dc=bigrs-info,dc=com (sAMAccountName=%u)


