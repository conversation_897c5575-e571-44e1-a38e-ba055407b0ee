# 加载 PAM 模块
<IfModule mod_auth_pam.c>
    # 配置 PAM 使用的服务名称
    AuthPAMConfig proftpd      # 指定 PAM 配置文件名，默认是 proftpd
    # AuthPAM On                  # 启用 PAM 认证
</IfModule>

ServerName                      "ProFTPD FTP Server for VAP"
ServerType                      standalone
DefaultServer                   on
DefaultRoot /mnt
PassivePorts                  50000 50010

# <IfModule mod_tls.c>
#     TLSEngine                   on
#     TLSLog                      /var/log/proftpd/tls.log
#     TLSProtocol                 TLSv1.2
#     TLSRSACertificateFile       /etc/proftpd/ssl/proftpd.crt
#     TLSRSACertificateKeyFile    /etc/proftpd/ssl/proftpd.key
#     TLSOptions                  NoCertRequest
#     TLSVerifyClient             off
# </IfModule>
